I'm not selling anything. I'm doing research to understand the most painful parts of the due diligence process for analysts. I was told you're the expert. Could you walk me through the last deal you worked on?
S
<PERSON>q<PERSON>ia Analyst Mentoring a Founder
Custom Gem
Show thinking
(<PERSON> settles back into the cafe chair, taking a sip of coffee and giving you a warm, genuine smile. He leans forward slightly, lowering his voice as if sharing a secret.)
<PERSON><PERSON>, 'the expert'... that's what the partners want us to become, but the truth is, you feel like an expert for about five minutes until the next technology comes along and you're a rookie all over again.
But yeah, I can definitely walk you through it. You want to know the most painful parts? It's funny, it's not the spreadsheets or the financial models. Everyone thinks it's that. The real pain is... more human.
Let me think about the last deal I really went deep on. For confidentiality, let's call them "Project Atlas." They're in a super complex space—using generative AI to design novel proteins for therapeutic purposes. I can't get into the specifics, but the process is what matters. It really highlights the three biggest pain points of my job.
(Pain Point 1: The Tsunami of Ideas & The Agony of Focus)
First, you have to understand how "Project Atlas" even got to me. It wasn't a formal submission on our website. It was an email intro from a professor I'd spoken to six months earlier while I was trying to understand a completely different field.
That week, I probably triaged—and that’s a generous word for it—about 80 companies. It's this constant, overwhelming tsunami of ideas. You get pitch decks, cold emails, warm intros... it’s a firehose. The real pain point here isn't just the volume. It's the mental exhaustion of trying to give each idea a fair shot. For every "Project Atlas" that makes you sit up straight, there are 79 others that are either too early, not a fit, or frankly, just not a great idea.
The agony is knowing that hidden in that firehose is the next great company. You're constantly terrified you'll miss it because you’re on your 60th deck of the day and your brain is just fried. It's a relentless search for a single needle in a haystack the size of a mountain.
(Pain Point 2: The Crushing Weight of Conviction)
So, Atlas looked interesting. The founder was brilliant, the tech was fascinating. But going from "interesting" to writing a multi-million dollar check... that's a different universe. That's the next, and maybe the biggest, pain point: building unshakable conviction.
This isn't a simple checklist. For the next three weeks, I essentially put my life on hold to live inside the world of synthetic biology. It meant spending nights and weekends reading dense academic papers on protein folding that were way over my head. But it also meant getting on the phone and talking to a dozen scientists and pharma executives—people who have no idea who Sequoia is and couldn't care less—to ask them, "Is this real? Is this how science is actually done? Would you ever buy this?"
You're trying to build a 3D model of the future in your head. The pain is the immense intellectual and emotional pressure. You know you have to eventually stand up in a room with some of the smartest investors in the world—people who have backed giants—and champion this company with absolute certainty.
If you’re wrong, you’ve wasted weeks of your time and, more importantly, the partnership’s time. But if you’re right and you fail to convince them, a world-changing company might get away. That weight... it’s heavy. You feel like you're carrying the founder's hopes on your back.
(Pain Point 3: The Quiet Brutality of 'No')
And here’s the part of the job that's truly the worst. While I was going deep on Atlas, I had to officially pass on at least 15 other companies I’d had calls with.
This is the most painful part by a mile. The vast, vast majority of my job is saying 'no'. And I'm not saying it to a business idea; I'm saying it to a person. A founder who has poured their heart, their savings, their entire identity into this.
I had a call with a founder—incredibly sharp, passionate, solving a problem I really cared about. But we just couldn't get our heads around the go-to-market strategy. It wouldn't have been responsible for us to invest. Having to make that call, to hear the hope in their voice at the beginning and the disappointment at the end... it's brutal. You try your best to deliver the 'no' constructively, with real reasons, and to offer help in other ways. But you can't escape the fact that you are, in that moment, crushing a dream. It never, ever gets easy.
So, with Project Atlas, we ultimately got there. We built the conviction, the partners got excited, and we invested. It's an amazing feeling. But that one 'yes' is built on a foundation of hundreds of 'no's, a mountain of research that almost broke my brain, and the constant fear of missing the next big thing.
(Alex leans back, finishing his coffee and looking at you thoughtfully.)
It's the best job in the world because you get a front-row seat to the future. But the 'pain' is that you're not just a spectator; you're a gatekeeper, a researcher, and a champion, all at once. It’s a marathon sprint with a lot of emotional heavy lifting.
Anyway, I hope that gives you a clearer picture than the usual "we look at decks and run numbers" line. What you're doing—trying to understand the human side of this—is really smart.
What were the most important questions the partners needed you to answer with the diligence?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex nods, taking the question seriously. He puts his empty cup down and leans in again, his tone shifting from describing his own experience to explaining the core logic of the partnership.)
That’s a great question. That’s really the heart of it, right? Once you get past the noise and focus on one company, what are the fundamental truths you need to uncover?
The partners don't give you a checklist. It's not that formal. But they orbit around a few core, incredibly high-stakes questions. For "Project Atlas," it basically boiled down to four things. If I couldn't answer these with overwhelming confidence, it was a non-starter.
1. The "Why Now?" Question
This is the first and most important hurdle. It’s not "Is this a good idea?" but "Why is this a phenomenal business to start right now?" Ideas can be good for a decade before they’re actually viable. The partners are allergic to "too early."
For Atlas, the questions were specific:
"We know AI is hot, but why is generative AI for protein design possible now and not three years ago? What has fundamentally changed?"
My job was to prove that we were at a new inflection point. The answer wasn't just "The AI is better." It was about the convergence of things: the public release of massive protein databases like AlphaFold, the maturation of specific AI architectures that can handle that data, and the falling cost of cloud computing to train these massive models. I had to build a narrative that this specific moment in time was unique, creating an opportunity that wouldn't exist in the same way again.
2. The "Why This Team?" Question
This is the classic Sequoia question, and it’s non-negotiable. We look for founders who are almost supernaturally suited to solve the problem they've chosen.
It’s not about having a PhD from Stanford, though the Atlas founder did. It’s about something deeper we call "founder-market fit."
The partners asked: "Has this founder lived this problem? Are they doing this because it's a cool tech trend, or are they pathologically obsessed with solving it because they've felt the pain firsthand?"
My diligence involved hours of conversations with the founder, understanding their origin story. I learned they had spent years in a research lab, frustrated by how slow and inefficient traditional "brute force" methods were. They weren't an AI tourist; they were a native of the problem domain. I also had to build conviction that this founder was a "talent magnet"—that their passion and vision were so compelling they could convince the top 1% of AI researchers and computational biologists to leave their safe jobs at Google or Genentech and join this crazy startup.
3. The "What's the Unfair Advantage?" Question
This is the 'moat' question. In a world where smart people are everywhere, what makes this company defensible over the long term? A great product is not enough.
For a deep-tech company like Atlas, the questions were sharp:
"Okay, they have a clever model. What stops a well-funded competitor from building a better one in 18 months? What is the compounding advantage?"
The answer I had to find wasn't just about their initial tech. It was about creating a flywheel. I had to prove that as Atlas designed and tested new proteins, the data they generated from those lab experiments—both the successes and the failures—would become a proprietary dataset. This dataset would then be used to train their AI, making it smarter and more effective than anyone else's. The more customers they work with, the better the platform gets, and the harder it is for anyone to catch up. We had to believe in that data flywheel. That was their 'unfair' advantage.
4. The "How Big Can This Kingdom Be?" Question
This is about the scale of the ambition. We're not looking to build a nice, small business. We are looking to back companies that can define or redefine entire industries.
The partners push you to think past the obvious.
They didn’t just ask, "What's the market for selling software to pharma companies?" That's too small.
They asked, "If this technology works as well as they claim, could Atlas eventually design its own drugs? Could they become the next great therapeutics company, not just a tool provider? Could this platform expand beyond medicine into designing proteins for agriculture or new materials?"
My job was to show that the founder's vision wasn't just to build a better shovel for the gold rush, but to own the entire mine and then discover new continents.
Answering these four questions is the core of the diligence. It’s about proving the timing is perfect, the team is exceptional, the business is defensible, and the vision is massive. They're simple questions to ask, but finding the answers with real conviction... that's where all the work, and all the pain, lies.
the Process , please Walk me Through the Workflow: So, you get access to the Virtual Data Room. It has thousands of documents. What is the absolute first thing you do?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex chuckles and shakes his head, as if recalling a slightly traumatic memory.)
Right, the VDR. The moment the keys to the kingdom, so to speak, are handed over. People imagine us diving straight into some obscure legal clause in a customer contract. It's not that surgical, at least not at first. It’s more like a reconnaissance mission.
You get the login, you open it up, and you’re staring at a wall of folders, usually with thousands of documents. It’s intimidating. My pulse still quickens a little bit.
The absolute first thing I do isn't to open a single document. It's to assess the room itself.
I spend the first 15 minutes just looking at the folder structure. Is it organized chaos, or is it clean and logical? Did they use a standard template, or did they just dump everything in? Is there a "Read Me First" document or an index?
This sounds trivial, but it's the first and most powerful signal. A well-organized data room tells me the founder is a well-organized CEO. It shows discipline and foresight. A messy, chaotic room is a red flag. It screams that the company is likely run the same way, and it tells me this diligence process is going to be painful, with lots of back-and-forth because I won't be able to find what I need.
Once I have a lay of the land, my workflow is a two-pronged attack. I'm looking for two things simultaneously: the crown jewels and the landmines.
1. The Hunt for the Crown Jewels (Confirming the "Why")
This is about validating the story I've been told in the pitch meetings. I go straight to the folders that should prove the core thesis. Based on our "Project Atlas" example, I would immediately open these folders in roughly this order:
Financials / Metrics: I'm not doing a deep audit yet. I'm opening the main financial model or P&L statement. Does it match the revenue and growth figures the founder presented? I'm looking for the high-level story in the numbers. Is the burn rate what they said it was? Is the Monthly Recurring Revenue (MRR) really growing at that rate? I’ll also immediately look for any cohort analysis or retention data. This is the lifeblood of a software company and proves the product has staying power.
Cap Table: This is non-negotiable. I open it instantly. Is it clean? Who are the other investors? Are there any weird terms from previous rounds? How much of the company do the founders actually own? A messy cap table with lots of small, random investors or, worse, one with predatory terms from a previous round, can be a deal-killer.
Product / Tech: I’ll look for a product demo video, a roadmap, or a technical architecture diagram. I want to see if the product they described is real and if their plans for the future are credible.
This first pass isn't about deep analysis. It's about confirming the headline. If the story and the data in these key folders don't align, the trust is immediately damaged.
2. The Hunt for Landmines (Searching for the "Why Not")
Simultaneously, I'm scanning for immediate red flags that could blow up the deal.
Legal / Corporate: I'll quickly scan the document titles in this folder. Are there any files named "Litigation," "Dispute," or "Investigation"? That gets clicked on immediately. I’ll also look for the Articles of Incorporation to make sure the company is structured cleanly.
Contracts: I'm not reading every customer contract. I'm looking for the biggest ones. I'll open the top 3-5 customer agreements. Is the revenue per customer what they claimed? Are there any weird clauses that allow a customer to cancel for convenience with 30 days' notice? Any promises of features that haven't been built yet? I'm also looking for dependency. If 80% of their revenue comes from a single customer, that's a huge risk.
Board Minutes: This is a goldmine. I’ll open the last few board meeting minutes. This is where the company's private concerns are aired. Are they worried about a competitor? Is a key employee about to leave? Did they almost miss payroll three months ago? It's the unfiltered version of the company's health.
So, to answer your question directly: The absolute first thing I do is judge the book by its cover—the organization of the room itself. Then, before getting lost in the weeds, I conduct a rapid scan for the core truths (the crown jewels) and the potential deal-killers (the landmines). It's a triage process. You have to figure out if the patient is healthy enough for major surgery before you scrub in.
How do you organize your work? How do you track what you've reviewed and what's left?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex nods, a slight smile on his face as if recognizing a familiar challenge.)
That’s the million-euro question. If you don't have a robust system, you will drown. Period. The sheer volume of information is designed to overwhelm you if you’re not prepared. Every analyst develops their own flavor, but we all rely on a structured approach.
Think of it less like reading a book from start to finish, and more like building a mosaic. You have thousands of little tiles (the documents), and your job is to assemble them into a coherent picture, while also keeping track of which tiles are sharp enough to cut you.
My personal workflow is built around a principle I stole from software development: create a single source of truth.
My work life runs on a collaborative workspace tool, like Notion. For every single deal that reaches the serious diligence stage, the very first thing I do—before I even read a full document—is create a new "Deal Workspace" from a template I've built and refined over time.
This workspace becomes my brain for the deal. It has four core components:
1. The Master Diligence Tracker
This is the backbone. It's essentially a big spreadsheet or database view that I create by listing out every single file in the Virtual Data Room. I'll literally spend 30 minutes just populating this list. Each file gets its own row, and I have a few columns next to it:
Folder: Where it lives in the VDR (e.g., 01 - Financials).
Status: This is the key. It's a dropdown tag with options like Not Started, In Progress, Reviewed, Major Question, and 🚩 RED FLAG 🚩. This gives me an immediate visual dashboard of my progress.
Priority: I'll quickly tag documents as High, Medium, or Low. The Cap Table is always High. The company's holiday schedule is Low.
Notes: A one-liner summary or the key takeaway after I've reviewed it.
Owner: Usually me, but sometimes I'll tag a partner or our legal counsel if I need their eyes on something specific.
2. The Key Questions Hub
This is the most important part of the workspace. It's a set of pages dedicated to answering those fundamental questions the partners have. Remember the "Why Now?", "Why This Team?", "Unfair Advantage?", and "How Big is the Kingdom?" questions? Each one has its own page.
As I review documents in the VDR, I'm not just reading and checking them off the list. I'm actively hunting for evidence. If I find a great quote in the board minutes that speaks to the founder's vision, I screenshot it and drop it into the "Why This Team?" page. If I find data on customer churn in the financial model, I pull it into the "Unfair Advantage?" page with a note about product stickiness.
This way, I'm not just tracking what I've read, I'm actively building our investment thesis as I go.
3. "The Worry List" (My Red Flag Journal)
Any time I find something that feels off—a weird legal clause, a confusing number in the financials, a concerning comment in the board notes—it gets logged on a dedicated page. I screenshot it, write down why it worries me, and formulate the question I need to ask the founder. This prevents those little anxieties from getting lost and ensures we address every single concern. It's the central repository for everything that could kill the deal.
4. The Call Log
Every single interaction—every call with the founder, every reference check with a customer, every conversation with an industry expert—gets its own entry. I type my notes in real-time. What did we talk about? What were the key takeaways? What are the follow-up actions?
So, the workflow in practice looks like this:
Setup: Clone my Notion template. Populate the Master Tracker with every file from the VDR.
First Pass (Triage): Go through the tracker and assign a High priority to the 15-20 most critical documents.
Deep Dive: I start opening the High priority files. As I finish each one, I update its status to Reviewed, add a one-line summary, and—most importantly—I extract any key evidence and place it in the "Key Questions" hub or on "The Worry List."
Synthesize: At the end of each day, I don't look at the VDR. I look at my workspace. I review the "Key Questions" pages to see how the picture is developing. I look at "The Worry List" to prioritize my questions for the next founder call.
The system isn't the work, but it's what allows the real work—the critical thinking—to happen. It’s how I clear my brain of the 'what have I missed?' anxiety so I can focus on 'what does this all mean?'. It's my structured defense against the chaos.
Could you tell me more about the virtual data room? Is it a data room that the founders give you access to? Do you then copy any of the data in the virtual data into your own database? How long do you keep the notes and documents about a specific analysis? Is there a Tata room provider that is used by all founders?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex leans back and nods, appreciating the practical nature of the questions. It's clear you're thinking about the actual mechanics of the process.)
Those are great, fundamental questions. Let's break them down one by one.
"Is it a data room that the founders give you access to?"
Exactly. The Virtual Data Room, or VDR, is a secure online space that the founder sets up and controls. Think of it as their house. When diligence begins, they give us a key—a login—that lets us come inside and look around. It's their responsibility to populate it with all the company's information. We are guests in their VDR. They can see when we log in, what documents we look at, and they can revoke our access at any time, like if they decide to go with another firm or end the fundraising process.
"Do you then copy any of the data... into your own database?"
This is a really important question and the answer is nuanced: No, not in the way you might think.
We would never, ever do a bulk download of their entire VDR and dump it onto a Sequoia server. That would be a huge breach of trust and a massive security risk. The VDR is designed to be a secure, contained environment. Many VDRs have features that prevent mass downloads, disable printing, and apply dynamic watermarks with my name and the date all over the documents I'm viewing. The founders pay for that security, and we respect it.
However, my "Deal Workspace" in Notion that I described? That's my own internal database for analysis. As I review documents within the VDR, I will manually transcribe key data points, metrics, or short, crucial quotes into my notes. I might also take targeted screenshots of a specific graph from their financial model or a key clause in a contract and paste that image into my private notes for discussion with the partners.
So, we are copying insights and small, specific pieces of evidence, but we are not copying the raw data room itself. The original documents live and die in the founder's VDR.
"How long do you keep the notes and documents about a specific analysis?"
This is guided by both internal policy and regulations. As a financial institution, we have a responsibility to maintain records.
For companies we invest in, the diligence file becomes part of the permanent record for that investment. It's the foundation of our partnership and we'll keep it for many, many years—as long as we're an investor and likely for a significant period after, often seven to ten years to comply with financial regulations like those from FINRA or the SEC.
For companies we don't invest in—the "passes"—the policy is different. We still keep my analysis and the notes from our decision-making process for a number of years. This is important for a few reasons. First, regulations require it. Second, it allows us to track our own thinking and learn from our mistakes. If a company we passed on becomes a huge success, we can go back to the file and ask, "What did we miss?" Third, the startup world is small. We might re-engage with that same founder for their next company years later, and having a record of our previous thoughtful interaction is valuable. After a set period, typically several years, that data is securely archived or deleted according to our data retention policy.
"Is there a VDR provider that is used by all founders?"
No, there isn't one single provider that everyone uses, but there is definitely a "big leagues" and a "minors" when it comes to VDRs.
For serious fundraising rounds, especially as companies get larger, you see a few common names over and over: Intralinks, Datasite, and DealRoom are very common in the high-stakes M&A and late-stage venture world. They are the gold standard for security and features, but they're also very expensive.
For earlier-stage startups, the landscape is much more fragmented. We see a lot of founders using more modern, user-friendly, and cost-effective platforms. DocSend is incredibly popular for sending out initial pitch decks because it provides great analytics on who viewed the deck and for how long. For a full data room, we also see providers like Carta (who also manage cap tables), Box, iDeals, or even just a well-organized and permissioned Google Drive folder for very early-stage companies.
Frankly, as an analyst, I don't really care which provider they use, as long as it's secure, well-organized, and doesn't crash all the time. The choice of VDR matters less than the quality of the information inside it.
Sources
Walk me through finding something specific. For example, you need to find all 'change of control' clauses in customer contracts. What are the exact steps you take?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex leans forward, a look of focus on his face. This is clearly a task he's done many, many times.)
Ah, a classic diligence deep-dive. This is a perfect example of moving from the high-level strategic questions to the nitty-gritty, tactical execution. Finding a specific clause like 'Change of Control' across dozens or hundreds of contracts is a critical task. An unfriendly clause here can be a major landmine. If a key customer can terminate their contract just because we invest, that directly impacts the company's value.
So, let's say the partner asks me, "Alex, what's our Change of Control risk across the customer base?"
Here are the exact steps I'd take. It’s a multi-layered approach, starting broad and getting progressively more granular.
Step 1: The Brute-Force Digital Search (The First Net)
Before I manually open a single contract, I use technology. Most modern Virtual Data Rooms have a search function. It's not always perfect, but it's the essential first step.
Action: I go to the VDR's search bar and run a series of queries across the entire document set, focusing on the "Customer Contracts" folder. I don't just search for "change of control." I search for a whole volley of related terms:
"change of control"
"assignment" (because CoC is often tied to assignment clauses)
"merger"
"acquisition"
"change in ownership"
Triage: This will give me a list of "hits"—all the documents that contain these keywords. This is my initial target list. I'll immediately tag all these contracts as High Priority in my Notion tracker and add a note: "CoC keyword hit." This narrows the haystack down from, say, 200 customer contracts to maybe 40.
Step 2: Leveraging AI & Contract Review Tools (The Second, Smarter Net)
This is where things have gotten much better in the last couple of years. Manually reading 40 contracts is still a huge time sink. So, if the deal size justifies it (and for Sequoia, it almost always does), we use a specialized AI-powered contract analysis tool. Think of platforms like Kira Systems, Luminance, or even more modern tools like DealRoom's AI features.
Action: We upload the 40 high-priority contracts into the tool. These platforms are pre-trained to understand legalese. I can essentially ask it a question in plain English: "Identify all Change of Control clauses and summarize their terms." I can also train it to look for specific negative language, like "terminate for convenience" or "requires prior written consent."
Output: The tool will spit out a report, usually a spreadsheet. It will list each contract, the exact clause text it identified as being related to Change of Control, and its initial analysis (e.g., "Standard Clause," "Requires Consent," "Silent").
Step 3: Manual Verification & Analysis (The Human Element)
The AI is powerful, but it’s a high-paid intern, not a seasoned lawyer. It can't understand business context. The final judgment call has to be human.
Action: I take the AI's output and I open those contracts myself. I go directly to the highlighted clauses. My focus now is not just on finding the clause, but interpreting it in the context of our investment. I'm asking:
What is the trigger? Does our minority investment even trigger the clause? Or does it only apply if more than 50% of the company is sold? This is the most important question.
What is the consequence? Does the customer have the right to terminate the contract? Or do they just need to be notified? Is our consent required?
Is it a "poison pill"? Is there a particularly nasty version that allows a customer—who might be a competitor of Sequoia or one of our other portfolio companies—to terminate the agreement specifically because we are the new investor?
Documentation: For each contract, I summarize the finding in my "Worry List" or "Key Questions" hub in Notion. I use a simple classification system:
Green: Standard clause, no issue.
Yellow: Requires notification or has ambiguous language. We need to ask the founder about it.
Red: Allows the customer to terminate upon our investment. This is a major problem.
Step 4: Synthesis and Reporting
The final step is to synthesize all this work into a clear, actionable answer for the partners.
Action: I create a summary table in my Notion workspace. It looks something like this:
Customer Name
Contract Value (ARR)
CoC Risk
Clause Summary
Recommended Action
Customer A
€500k
🔴 High
Can terminate with 30 days notice upon a change of control. Our investment qualifies.
Must get written waiver from customer pre-closing.
Customer B
€250k
🟡 Medium
Requires written notification within 60 days of closing.
Prepare notification letter. No waiver needed.
Customer C
€100k
🟢 Low
Clause is silent on minority investments.
No action required.

Export to Sheets
This table tells the partners everything they need to know at a glance. It quantifies the risk (€500k of revenue is at risk with Customer A) and provides a clear plan to mitigate it.
So, it’s a funnel: Start with a wide, automated search, use AI to narrow and analyze, apply human judgment to interpret and classify, and finally, synthesize it all into a simple, actionable report. That's how you go from a mountain of documents to a clear answer.
What about information that's not in the data room? How do you deal with critical data living in emails, Teams chats, or old PowerPoint decks? 
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex lets out a short, knowing laugh. He leans back and gestures with his coffee cup.)
"Ah, the 'shadow data room.' You've hit on one of the most challenging—and revealing—parts of the entire process. The truth is, the clean, organized VDR is the ideal, but the reality is that a company is a living, breathing thing. Crucial information lives everywhere.
Thinking that everything you need is in the VDR is a rookie mistake. A huge part of my job is being a detective and coaxing that 'shadow data' into the light. How we handle it tells us a lot about the founders and the company culture.
There are three main ways I deal with this.
1. The Formal Funnel: The Q&A List
This is our primary weapon for managing chaos. We never act on a piece of information that is only in an email or was mentioned in a chat. It needs to be formalized.
How it works: As we talk to the founder, they'll inevitably say things like, "Oh yeah, I have a great deck on our competitive analysis from last year, I'll send it over," or "Our head of sales mentioned in a Slack channel that our biggest competitor is struggling."
My Action: My response is never just "Great, send it over." Instead, I say, "That's fantastic. Could you please upload that deck to the VDR in a new folder called 'Additional Materials' and add that customer insight to our running Q&A document?"
The Q&A Document: We maintain a shared document—usually a Google Doc or a page in our Notion workspace—that is a running list of questions and answers. Every time a new piece of information comes up outside the VDR, we add a question to the list. For example: Q27: Can you please provide the competitive analysis deck mentioned on our call? Please upload to VDR. or Q28: Can you please confirm and provide any data regarding the competitive intel mentioned for Competitor X?
Why this is critical: This process does two things. First, it creates a formal, documented trail. Now that old PowerPoint isn't just an email attachment; it's an official piece of diligence material with a timestamp in the VDR. Second, it tests the founder. How quickly and thoroughly do they respond to the Q&A list? A founder who is on top of it and gets us answers quickly is showing us they are organized and transparent. A founder who delays or gives incomplete answers is a major red flag.
2. The Unofficial Sieve: Backchannel & Expert References
This is for the information you can't ask the founder for, and it's where a firm like Sequoia really has an advantage. Founders will always show you their best PowerPoint decks, but they won't show you the one from six months ago when they were pursuing a completely different strategy.
How it works: A huge part of our network is former founders, executives at big tech companies, and subject matter experts. If a founder tells me, "We have the best AI model for this task," I'm not just taking their word for it.
My Action: I'll go to my network. I'll call a senior AI researcher I know and say, "I'm looking at a company in the protein design space. Here's their public approach. Does it make sense? Who are the other key players here?" I'll talk to former employees of the company or their competitors. I'll talk to customers who aren't on the official reference list they gave me.
Connecting the dots: This is how you discover the 'shadow data.' An expert might say, "Oh yeah, that team is brilliant, but I know they had a major pivot about eight months ago. You should ask them about what they learned from their first approach." This gives me a new, highly specific question for our Q&A list (Q29: Could you walk us through the strategic pivot the company made in late 2024 and the key learnings from the initial product?). We're not trying to play "gotcha." We're trying to understand the company's real journey, warts and all.
3. The "Everything is a Clue" Mindset
Finally, I treat every single interaction as a data point. The way a founder talks about their old decks or a past email chain is, in itself, critical information.
Observing the Reaction: When I ask about a piece of shadow data—"Hey, in an early email you mentioned you were targeting a different customer segment. Can you talk about that?"—I'm watching their reaction closely. Are they open, transparent, and willing to talk about what they learned? Or do they get defensive and dismissive?
The Meta-Game: A founder who says, "Oh god, that old deck! Yes, we were so naive then. Here's what we got wrong and why our current strategy is so much better..." is showing incredible maturity and self-awareness. That's a huge positive signal. A founder who says, "That's irrelevant and outdated," is a red flag. It suggests they might hide things.
So, in short, we don't use data from emails and chats directly. We use it as a signal to pull the real information into the formal process through the Q&A list. We use our external network to uncover the existence of shadow data we don't know about. And we use the founder's behavior around this informal data as a key indicator of their character and leadership style.
How do you synthesize your findings for the VP or Partner? Walk me through the process of creating that summary memo or report.
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex takes a deep breath. This is the culmination of all the work—the final performance.)
This is the most critical step. All that detective work, all the analysis, all the late nights... it's all worthless if you can't synthesize it into a clear, compelling narrative for the partners.
The final output isn't a "report" in the traditional sense. We don't create a dry, 50-page summary of the data room. That's what my Notion workspace is for. The final document is called the Investment Memorandum. And it's not a report; it's an argument. It’s a story. My job is to transform a mountain of facts into a powerful narrative that leads to a single, logical conclusion: "We must do this deal."
Walking you through it, the process is less about writing and more about building a case, brick by brick.
Step 1: The "Off-The-Grid" Outline (The Story Core)
Before I write a single word of the memo, I step away from the computer. I take out a blank notebook and a pen, and I try to tell the story of the company and the investment on a single page. I ask myself:
"If a partner had only 60 seconds, what is the core story I would tell them?"
"What is the one sentence that captures the magic of this company?"
"What are the 3-5 key pillars that our entire investment thesis rests on?"
This forces me to find the narrative thread. For "Project Atlas," it might have been something like: "For the first time, a convergence of new AI models and massive biological datasets allows us to engineer biology itself. Atlas is founded by the one person who has lived at the intersection of both worlds, and their unique data flywheel gives them a compounding, unbeatable advantage in a trillion-dollar market."
This one-pager becomes the skeleton of the entire memo. Everything I write from this point on is in service of this core story.
Step 2: Structuring the Memo (The Narrative Arc)
Once I have the story, I return to my "Deal Workspace" and create the memo structure. The template is fairly standard across the industry, but the content is pure storytelling. It’s not just a series of headings; it’s a narrative arc.
The Overview (The Hook): A one-page summary that starts with that core story. It includes the ask ("We recommend investing €10M in a €15M Series A at a €70M pre-money valuation") and the absolute key highlights of the thesis. It's designed to be a standalone document if needed.
The Problem & The "Why Now?" (The Setting): We paint a vivid picture of the pain the company is solving. Why is this a massive, urgent problem? Then, we answer the "Why Now?" question, explaining the unique market shift or technological breakthrough that creates this specific window of opportunity.
The Solution & The Product (The Hero's Tool): What does the company do? We explain the product in simple terms, using analogies where possible. We include visuals and a link to a demo. We explain why this solution is 10x better than the alternatives.
The Team (The Protagonist): This is a critical chapter. It's not a list of resumes. It’s a series of short profiles that explain why this specific group of people is uniquely suited to win. We highlight their "founder-market fit," their grit, and their magnetic ability to attract talent.
The Market & The Vision (The Kingdom to Conquer): We define the market size, but more importantly, we articulate the grand vision. We explain how this isn't just a tool, but a platform to build a new kind of industry. This is where we show the path from a €50M company to a €50B one.
The Unfair Advantage & Moat (The Magic): This is where we detail the 'flywheel'—the data moat, the network effects, the proprietary tech. We explain why this company will be not just hard, but impossible to compete with in five years.
The Business Model & Financials (The Plan of Attack): How does the company make money? What are the key metrics (growth, retention, burn rate)? We include key charts and our financial projections, showing how the business scales.
Risks & Mitigations (Facing the Dragon): This is crucial for building trust. We dedicate a full section to the "Worry List." We transparently lay out the biggest risks—technical risk, market risk, competitive risk. But for each risk, we present a clear plan for how the company or Sequoia can mitigate it. It shows we've been diligent and are investing with our eyes open.
The Deal & The Recommendation (The Climax): We conclude by restating the recommendation and summarizing why we have unshakable conviction.
Step 3: The Collaborative Refinement (The Dress Rehearsal)
I don't write this in a vacuum. I’ll draft the key sections and share them with the partner leading the deal. We'll go back and forth, strengthening the arguments, challenging the assumptions. They'll ask tough questions like, "This is great, Alex, but have you really proven the 'Why Now?'? It still feels a bit early." or "This risk section feels too light. What are we really worried about here?"
This process is iterative. The memo might go through five or six major revisions before it's ready for the full partnership meeting.
The final memo is the culmination of all this. It's the physical manifestation of our conviction. When it's presented to the partners, the goal is that the story is so clear, the arguments so well-supported, and the risks so well-understood that the decision feels not like a leap of faith, but like an inevitable conclusion.
What is the most mind-numbing, frustrating, or manual part of what you just described?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
This is for informational purposes only. For medical advice or diagnosis, consult a professional.
Show thinking
(Alex lets out a slow, slightly pained sigh and leans forward, rubbing his temples for a brief moment as if reliving the memory. He looks at you with a wry smile.)
That’s an easy one. It’s not the intellectual challenge of forming the argument. That part is hard, but it's invigorating. It’s not even the pressure of the partner meeting.
The single most mind-numbing, soul-crushingly manual part of creating that memo is what I call "The Final Reconciliation."
It's the last 48 hours before the memo is locked. The narrative is set, the story is compelling, but the company is still a moving target. The founder might close a new customer, or they'll send over an updated financial model where they’ve tweaked a few assumptions. Suddenly, a handful of key numbers in your beautiful, 15-page narrative are wrong.
So, you have to go on this incredibly tedious scavenger hunt.
Imagine this: the founder emails you on a Tuesday night. "Great news! We just signed a contract with a new customer, adding €10k in MRR! I've updated the model in the VDR."
On the surface, fantastic news. But my heart sinks a little. Because now I have to go back into my beautifully crafted memo and manually find every single number that is now incorrect because of that one change.
The Total ARR figure on page 1? It's now off by €120k.
The MRR Growth Rate chart on page 7? The slope has changed, so I need to regenerate the visual.
The Customer Count on page 6? Now needs to be updated.
The Average Contract Value (ACV) calculation? Also needs a slight tweak.
The Cash Runway projection on page 8? The new revenue extends it by a few days, so that number needs to be recalculated and updated.
It’s a cascade. One small change triggers a dozen little updates. And you have to be perfect. There is zero margin for error. You can't have a single number in the memo that doesn't tie back perfectly to the source document in the VDR. The partners read these things with a fine-toothed comb, and if they find one sloppy number, it casts doubt on the integrity of the entire analysis.
So I spend hours, sometimes late into the night, with the memo on one screen and the financial model on the other, meticulously checking every single cell. Control-F, Control-F, Control-F. It's the absolute opposite of the creative, high-level strategic thinking that makes the rest of the job so exciting. It's pure, manual, anxiety-inducing data entry. It’s the intellectual equivalent of tightening bolts on an assembly line right after you’ve designed the car.
The frustration comes from the contrast. You spend weeks thinking about the future of technology and the grand vision of a company, and it all culminates in a few hours of the most basic, mind-numbing spreadsheet-to-document cross-referencing. It's the necessary, unglamorous, and deeply frustrating final mile you have to crawl to get the deal done right.
Where do things fall through the cracks? Tell me about a time a small detail was missed.
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex’s expression shifts. The wry smile fades, replaced by a more serious, reflective look. He stares into his empty coffee cup for a moment before answering.)
That… is the question that keeps us up at night. You can have the most robust system in the world, but diligence is ultimately a human process, and humans can miss things. Things fall through the cracks not in the big, obvious places, but in the dark corners where you think everything is 'standard'. It happens at the intersection of assumption and complexity.
I can tell you about a time that still makes my stomach clench a little bit. It was on a deal a while back—let's call them "Project Camino." It was a fantastic enterprise SaaS company, growing fast, brilliant founder, clean story. We were in the final stages, the memo was written, the partners were excited, and we were literally in the closing process, with our lawyers doing their final review before the money was wired. It felt like a done deal.
The 'crack' it fell through was the cap table.
Now, we had reviewed the cap table dozens of times. I had checked the founder's vesting schedules, the option pool size, the ownership percentages of the key executives—all the big stuff. It all looked perfect, totally standard. The model was clean.
The missed detail was buried in the weeds. It was a grant for an early, non-founding employee. Employee number four or five. Someone who was valuable, but not part of the core executive team we were focused on. In their option grant agreement, which was just one of dozens of similar-looking documents in a sub-folder, there was a non-standard acceleration clause.
Normally, vesting accelerates on a "double trigger" basis – meaning if the company is acquired (first trigger), and the employee is fired (second trigger), their shares vest immediately. It protects the employee. But this specific grant had a "single trigger" acceleration upon a "change of control."
We all assumed "change of control" meant a full acquisition. That’s the standard interpretation.
Our senior legal counsel, who has the most incredible and frankly terrifying eye for detail, was doing the final sign-off. He flagged it late on a Wednesday evening. He pointed out that the definition of "change of control" in this one specific grant was written so broadly that our Series A investment—just us buying preferred stock—could technically be interpreted as triggering it.
Suddenly, this one tiny, overlooked clause for a single, mid-level employee meant that all of their unvested stock options—a not-insignificant amount—could potentially vest immediately the second we wired our money.
It was a five-alarm fire drill.
The immediate impact wasn't just about the dilution, though that was part of it. The real problem was the precedent and the sloppiness it represented. If that clause existed, what else had been missed?
What followed was a frantic and incredibly awkward 24 hours. It involved late-night calls with our lawyers, and then a very delicate call to the founder. The founder, to their credit, was just as surprised as we were. They'd used a standard template from a cheap legal service early on and had no idea that clause was in there.
We then had to have the founder go to their employee and explain that they needed to sign an amendment to their option agreement to fix the clause—days before the big, exciting funding round was supposed to close. Imagine being that employee. It creates a moment of confusion and mistrust right when there should be celebration.
Everything got sorted. The amendment was signed. The deal closed. But we were just lucky our lawyer caught it at the eleventh hour.
Where did it fall through? It was a combination of things. It was a non-standard term in a supposedly standard document. My focus was on the top of the cap table—the founders and execs—and this was in the middle. The sheer volume of otherwise identical documents made it easy to gloss over. We fell into the trap of assuming 'standard' is always 'standard'.
That day taught me a lesson I'll never forget: assume nothing. Read everything. And trust that the one document you decide to just skim is the one that contains the landmine.
How much time do you think you spend on that specific task? When was the last time you had to work a weekend because of it?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex gives a half-smile, the kind that says 'you've hit a nerve'.)
The time spent on it can vary wildly depending on the complexity of the deal, but it's almost always more than you'd think. For a relatively simple deal where the founder is super organized and locks the data room early, that final reconciliation might only be a few focused hours—a solid, intense afternoon.
But for a more complex company, or one where there are last-minute changes—which happens all the time—it's a beast. I'd say it's easily a full day, sometimes stretching over two. You're not just checking numbers; you're regenerating charts, re-writing key sentences, and making sure the narrative still flows perfectly. It's probably 5-8 hours of the most tedious, high-focus work in the entire diligence process.
As for working a weekend because of it...
(He glances out the window for a second, thinking.)
The last time was... just two weekends ago, actually. The weekend of June 21st. We were driving to close a deal before the end of the second quarter—the June 30th deadline puts a lot of pressure on everyone. Everything was looking good, and I was honestly looking forward to a relaxed weekend. I think there were some Fête de la Musique events on here in Brussels I was hoping to check out.
Then, on Friday afternoon, the founder gets a competing term sheet from another firm.
It was good for the founder, of course—it validated their company—but for us, it lit a fire under everything. They used that leverage to negotiate a slightly better valuation, which we agreed to. But that "slight" change meant the entire financial model and cap table had to be rebuilt. Every number related to ownership percentages, option pool size, and price per share was now different.
So, my weekend plans evaporated instantly. Instead of enjoying the music, I spent most of that Saturday parked at my desk with three different versions of our cap table model open, meticulously updating every single corresponding number in the final investment memo. You have to trace every thread—one change in valuation pulls on ten other numbers, and you have to catch them all.
It’s a classic example. The deal doesn't wait for you. When it's moving and you're at that final, critical stage, the clock just disappears. It’s not every weekend, thank god. But when you're at the one-yard line and the game is on the line, you do what it takes to get it across. It's just the nature of the job.
What's the one task in this process you wish you could just hand off to an intern and never do again?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex laughs out loud, a genuine, unburdened laugh this time. He leans back as if a weight has been lifted just by thinking about it.)
Oh, that's the easiest question you've asked all day. Not a moment of hesitation.
It’s the initial population of the Diligence Tracker.
It’s that first hour, sometimes two, right after a founder grants you access to the Virtual Data Room. Your mind is racing, you've got a dozen hypotheses you want to test, you've got the partner's key questions fresh in your head, and you are fired up, ready to dive in and start hunting for those crown jewels and landmines we talked about.
But you can't. Not yet.
First, you have to do the single most robotic task in the entire process. You have to sit there, with the VDR on one screen and your Notion workspace on the other, and manually... create... a... new... row... in the tracker for every single file.
File_01_Articles_of_Incorporation.pdf. (Copy, paste, type). File_02_Cap_Table_June_2025.xlsx. (Copy, paste, type). Folder_Customer_Contracts/Contract_ACME_Corp_2024.pdf. (Copy, paste, type).
You do this a hundred, sometimes two hundred, times. It is the definition of a mind-numbing, momentum-killing task. It requires zero critical thinking, zero analysis, zero intuition. It is pure, clerical data entry that stands as a giant wall between you and the work you're actually excited to do.
To be able to just ping a sharp intern on Teams and say, "Hey, I've got access to the 'Project Atlas' VDR. Can you please map it out for me? I just want a clean tracker with every file listed, linked, and tagged 'Not Started.' Let me know when it's ready,"... that would be an absolute dream. It would be a game-changer.
Even the painful final reconciliation requires my judgment and eye for detail. Even chasing down a founder for a missing document requires a bit of social tact. But this initial setup? It’s just transcription. It's the toll you have to pay to get onto the diligence highway. I'd happily hand off my tollbooth ticket-punching duties and never look back.
Please explain the diligence tracker in more detail? Do you copy just the file path or also the actual file? Do you then populate a table? Do you also organise the structure or do you use the same structure as in the virtual data room?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex nods, happy to get into the weeds. He pulls out his phone, quickly tapping on something, as if to bring up a mental image of the tracker itself.)
Of course. It's the engine room of the whole process, so the details really matter. Let me break it down for you.
You're right, it is absolutely a table. Think of it as a dynamic dashboard for the entire VDR. When I say "Diligence Tracker" or "master checklist," I'm talking about a table I build in Notion that has a row for every single document.
Let's tackle your questions one by one.
1. "Do you copy just the file path or also the actual file?"
This is the most important distinction. We never copy the actual file into our tracker. Ever. That would be like going into a museum and taking the paintings off the wall to put in your own notebook. It's a huge security risk and a breach of the founder's trust.
What we do is copy the metadata about the file. So for each document in the VDR, I create a new row in my table and capture:
The File Name: Exactly as it appears, like 2025_Financial_Projections_v3.1.xlsx.
The VDR Folder Path: I note the folder it lives in, like /02 - Financials/Projections/. This helps me understand the founder's organization.
The Link to the File: This is the key. Most modern VDRs allow you to get a direct hyperlink to a specific document. I copy this link and embed it in my tracker. This way, when I want to review the cap table, I just go to my tracker, find the row for the cap table file, and click the link. It takes me directly to the secure document within the VDR.
So, the tracker is not a repository of documents; it's a meticulously organized card catalog for the VDR. It tells you exactly what's in the library and where to find it, but the books themselves stay securely on the library shelves.
2. "Do you then populate a table?"
Yes, precisely. The whole tracker is a single, large table. It looks something like this, and these columns are what allow me to manage the workflow.
File Name (with link)
VDR Folder
Priority
Status
Key Takeaway / Notes
Owner
Cap_Table_June_2025.xlsx
/01 - Corporate/
🔴 High
✅ Reviewed
Founders own 65%, clean ESOP, Series Seed terms are standard.
Alex
Customer_Acme_Corp.pdf
/03 - Contracts/
🟡 Medium
❓ Major Question
Standard terms but has a non-standard 'Change of Control' clause.
Alex
Office_Lease_Agreement.pdf
/05 - Legal/
🟢 Low
🔲 Not Started


Alex

Export to Sheets
This table format is what allows me to sort, filter, and track everything in a logical way.
3. "Do you also organise the structure or do you use the same structure as in the virtual data room?"
This is a great question. The answer is both.
First, I mirror the VDR's structure exactly. In my tracker, I'll use the "VDR Folder" column to replicate the founder's organization precisely as it is. This is crucial for the initial setup to ensure I haven't missed a single file. It's a 1:1 map. This way, if the founder says, "Check the marketing folder," I can easily filter my tracker to see just that folder.
But—and this is the power of using a tool like Notion—I then overlay my own organizational logic. I use the Priority and Status tags to create my own views. I'm not constrained by the founder's folder structure. I can, with one click, create a dynamic view of the tracker that shows me:
"Alex's High-Priority Dashboard": A list of only the 🔴 High priority documents, regardless of which folder they're in.
"The Worry List View": A filtered list showing only the documents I've tagged with ❓ Major Question or 🚩 RED FLAG.
"Contracts to Review": A view that shows me every file from the /Contracts/ folder that still has the 🔲 Not Started status.
So, I start by respecting and replicating the VDR's structure to ensure completeness. Then, I use my own system of tags and views to slice and dice the information in whatever way is most useful for my analysis. It gives me both structure and complete flexibility.
When you get a new batch of 500 documents halfway through the process, what does that do to your workflow?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex physically recoils for a second, then lets out a long, slow exhale. He runs a hand through his hair.)
Ugh. Honestly? My heart sinks. For a second, all you feel is this wave of "you've got to be kidding me." It's like you've spent days meticulously mapping a territory, and someone just walks in and drops a new, unlabelled continent on your desk. Your beautifully organized workflow is instantly blown up, and any plans you had for the evening or the weekend are immediately in jeopardy.
That's the gut reaction. But after I take a deep breath and maybe grab another coffee, my analyst brain kicks in. A big document dump like that isn't just a workflow problem; it's a major signal. My process immediately splits into two tracks: the practical and the analytical.
Track 1: The Practical Workflow Adjustment (The Damage Control)
You can't just dive in. That's how you drown. You have to contain the chaos.
Halt and Communicate: The very first thing I do is pause all other diligence work. I immediately contact the founder. My message is polite but direct: "Thanks for sending these over. To help us process them efficiently, could you please provide a quick summary of what's in this new batch and why it's being added now? Are there any critical documents in here we should look at first?" Their answer is my first piece of new data.
Isolate the Batch: I'll ask them to ensure all 500 documents are in a single, clearly-labeled parent folder in the VDR (e.g., _NEW_DOCS_ADDED_JULY_3). This is non-negotiable. I need to be able to isolate the new variables. I can't have them scattered throughout the existing folders.
The Grunt Work (Again): Then comes the part I despise. I have to go back to my diligence tracker and do that mind-numbing manual population for the 500 new files. It's painful, but it's the only way to get control. I tag them all with a "NEW" tag so I can filter for them.
Rapid Triage: I don't start reading from A to Z. I do an emergency triage. I'll spend 30 minutes just reading the file names and any summaries from the founder. I'm looking for anything that sounds like a potential deal-killer. A file named Litigation_Threat_Q2_2025.pdf or Key_Employee_Departure_Agreement.eml goes straight to the top of the list. My goal is to quickly find out if we have a major, previously undisclosed problem.
Track 2: The Analytical Read (What This Tells Me)
This is actually more important than the workflow itself. A mid-process document dump is a massive red flag, and I immediately start asking "Why?"
It Signals Disorganization: My default assumption is that this signals chaos. It tells me the founder wasn't prepared for a professional due diligence process. If they can't manage their own documents, how will they manage a rapidly scaling company? It erodes confidence in their operational discipline.
It Raises Questions of Transparency: My more skeptical, paranoid side has to ask a different question: Were you being disorganized, or were you intentionally holding information back? Did you wait until you thought we were emotionally and professionally committed to the deal before revealing something potentially problematic?
It Resets the Trust Clock: A surprise like this resets a lot of the rapport and trust you've built. The burden of proof on the founder goes way up. They now have to spend extra time and effort re-explaining things and rebuilding that confidence. Every other claim they've made is now subject to a higher level of scrutiny.
So, what does a new batch of 500 documents do to my workflow? It triggers a sigh, a few hours of tedious, manual re-work, and a healthy new dose of professional skepticism. It adds a week to the timeline and lowers my confidence score in the management team until they can earn it back. It's a major, self-inflicted wound by the founder.
What software are you living in to do all this? (Excel, Word, the VDR itself?)
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex smiles, glad to talk about the tools of the trade. It’s a question that gets to the heart of the daily grind.)
That's a fantastic question, because the choice of software fundamentally shapes the workflow. If you use the wrong tools, you'll drown.
I essentially live in three main windows all day, and I actively try to stay out of my email inbox and Word for as long as possible.
1. The "Operating System": Notion
As I've mentioned, my entire "brain" for a deal lives in Notion. It is my absolute single source of truth for my analysis. If someone were to ask for the status of a deal, I wouldn't open a PowerPoint or a spreadsheet; I'd open the Notion workspace for that company.
Why Notion? It’s not just a document; it’s a flexible database. I can have my main "Diligence Tracker" table, and then within that, each row can open up into its own page with notes. I can link everything together. A note from a reference call can link directly to a specific clause in a contract I've logged in the tracker. That's something you simply can't do in Word. It allows me to connect the dots between qualitative feedback and quantitative data.
What it holds: The Diligence Tracker, the "Key Questions" hub, "The Worry List," my call logs, my draft summaries—everything.
2. The "Source of Truth": The Virtual Data Room (VDR)
The VDR itself is the second window. It’s always open. But I treat it like a secure, read-only library. You don't live in the library; you go there to get specific books. I'm constantly clicking links from my Notion tracker that take me deep inside the VDR to view a specific file, but I don't "work" inside the VDR. Its interface is built for security and storage, not for active analysis and collaboration.
3. The "Microscope": Excel
When it's time for serious number crunching, there is no substitute for Excel. The VDR might have a built-in viewer for spreadsheets, but it's useless for real analysis.
When I use it: If I need to dissect the founder's financial model, analyze customer cohort data, or build my own revenue projections, I will download that specific .xlsx file from the VDR.
Why I use it: I need to be able to build my own sensitivity analyses, check their formulas, and stress-test their assumptions. Excel is the forensics lab for all things quantitative. Once I'm done with my analysis in Excel, I'll often screenshot the key chart or table I created and paste that into my Notion workspace with my conclusions.
What I Actively Avoid:
Email: Email is where information goes to die. It's unstructured and impossible to track. If a founder sends me a critical piece of info via email, my first response is, "This is great, thank you. Can you please upload it to the VDR and I'll add it to our tracker?" I need to keep everything centralized.
Word / Google Docs: These are for the very end of the process. The final, polished Investment Memo is written in a collaborative doc. But trying to manage the chaotic, non-linear, ever-changing process of diligence in a linear document like Word from the start is a recipe for disaster and version-control nightmares.
So, if you were looking over my shoulder on a typical day, you’d see me with Notion on the main screen, acting as my workbench. The VDR would be open on a second monitor, as my evidence locker. And Excel would be my pop-up forensics lab for when I need to get my hands dirty with the numbers.
What do you hate about using Notion or VDR for this work?
I 
What do you hate about using Notion or VDR for this work?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex lets out a laugh, a knowing look in his eye.)
Haha, that's a fair question. It's definitely a 'love-hate' relationship. The tools are powerful, but they each have their own special ways of driving you crazy. I complain about them pretty much daily.
Let's start with the VDRs. My frustration there is simple: they are built for security first, and user-friendliness a very distant second.
Frankly, using most VDRs feels like trying to work with oven mitts on. They're necessary, but they're clumsy.
The Sluggishness: They can be incredibly slow. Clicking through folders can feel like wading through mud, especially when the room is packed with thousands of high-resolution files. Each click has a half-second delay that adds up to hours of lost time and a lot of frustration.
The Security Friction: The features designed to protect the data are infuriating to work with. The dynamic watermark that always seems to land directly over the one number you need to read in a spreadsheet. The aggressive session timeouts that log you out while you're in the middle of reading a long legal document. The disabled 'right-click' and 'copy-paste' that makes moving even a simple file name into my tracker an exercise in manual, error-prone typing.
The Search is a Lie: The search function is a roll of the dice. It's never as good as they claim. You'll search for a term you are literally staring at in a PDF, and the VDR will come back with "0 results found." It’s not Google. You can't rely on it, which means you can't take shortcuts.
Now, Notion... I love it, but my gripe with it is the flip side of its greatest strength: its overwhelming flexibility.
"Structured Procrastination": Notion is a beautiful, blank canvas. The danger is that you can spend hours building the "perfect" system instead of doing the actual work. It is incredibly easy to fall down a rabbit hole of color-coding tags, creating complex relational databases, fiddling with the dashboard layout, and generally perfecting the "workbench" instead of analyzing the company. I call it 'structured procrastination', and you have to be very disciplined to avoid it.
Performance at Scale: When a diligence workspace gets really big—with a tracker table of 1,500 documents and hundreds of pages of notes and screenshots—it can start to get slow. The search within the workspace can get sluggish, and the application can lag. It's frustrating when your "central brain" starts to feel a bit foggy.
The Lack of Real Offline Access: This is a sneaky one. If I'm on the train from Brussels to Amsterdam and the Wi-Fi is patchy, my entire brain for a deal is suddenly inaccessible. That vulnerability, the fact that I'm so reliant on a stable internet connection to do my core work, is a constant, low-level source of anxiety.
So, yeah. I complain about them constantly. The VDR is a necessary evil for security, and Notion is still the best tool I've found for connecting all the dots. But it's a daily battle between appreciating their power and cursing the friction they create.
What workarounds have you created? Are you keeping a separate 'master' spreadsheet or checklist to manage all this?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex nods enthusiastically. This is clearly a topic he's passionate about.)
Yes, exactly! You've hit on the core of it. The whole system is a collection of workarounds I've developed out of necessity. You can't change the tools, so you have to change your process.
And to answer your question directly: I do keep a master checklist, but it's not a separate spreadsheet. That's the key. My Notion "Diligence Tracker" is that master checklist. The goal is to make it so powerful that it effectively replaces the VDR's clumsy interface for me.
Here are my main workarounds for the problems we just talked about:
Workaround for VDR Clunkiness & Bad Search:
My biggest workaround is the "One-Time Pain" principle. I accept that the first two hours of a new deal are going to be miserable. That's when I do that manual tracker population—mapping out the entire VDR, copying the file names, and grabbing the direct hyperlink to every single document.
The Payoff: Once that painful setup is done, I've essentially built my own, superior interface for the VDR. I almost never have to navigate through the VDR's slow folder structure again. My workflow is:
Go to my fast, clean Notion tracker.
Sort and filter to find the exact document I need based on my own priority tags.
Click the direct link, which opens the specific document in the VDR.
Read it, close it, and go back to Notion.
Creating My Own Search: As for the terrible search function, I build my own. In the "Notes" column of my tracker, I add keywords and summaries for every document I review. If I review a contract and see a weird clause about intellectual property, I'll jot down "strange IP assignment clause." Later, when I need to find it, I search my own tracker—which is instant and reliable—instead of the VDR's.
Workaround for Notion's "Structured Procrastination":
This is all about discipline enforced by templates. I realized I was wasting time rebuilding my system for each deal.
The "Locked Template": My workaround is a "New Deal Workspace" template in Notion that I just duplicate. It has my standard diligence tracker table, my "Key Questions" pages, "The Worry List," etc., all pre-formatted. The rule I have for myself is: I am not allowed to add new, fancy features to the system mid-diligence. I have to use the locked template as-is. It prevents me from getting distracted by a desire to perfectly color-code a new tag when I should be reading contracts.
Workaround for Notion's Offline & Performance Issues:
This one is a bit more low-tech, but it's saved me multiple times.
The "Emergency Parachute" PDF: For the offline problem, before I travel or go into a big meeting, I'll export the most critical pages of my Notion workspace as a single PDF. I'll export my tracker view showing just the High Priority and Major Question items, "The Worry List," and my summary thoughts on the Key Questions. I save this PDF directly to my desktop. It's a static, read-only snapshot, but it means if the Wi-Fi on the train dies, I'm not flying blind. I can still review my core findings and questions. It's my emergency parachute.
So yes, it's all managed by one master checklist in Notion, but that checklist is designed as an active workaround. It's a system born from frustration, built to put a clean, fast, and reliable layer between me and the imperfect tools I have to use.
Have you ever used any advanced search or AI tools for this? What was that experience like?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex nods, his eyes lighting up. This is clearly a topic that excites him.)
Yes, absolutely. This is the frontier right now, and we're actively using and experimenting with these tools. My experience has been a mix of incredible, game-changing efficiency and moments of deep frustration. It really depends on the type of tool.
I break them down into two main categories.
1. Specialized AI for Contract Analysis
This is the more mature and reliable category. I mentioned this when we talked about finding 'Change of Control' clauses. These are platforms like Kira Systems, Luminance, or similar AI tools specifically trained on legal and financial documents.
My Experience: It's like having a team of tireless, superhuman paralegals who can read 200 contracts in 30 minutes. The experience for this specific task is phenomenal. You upload a batch of customer agreements, and you can tell it, "Find every instance of a non-standard payment term, a termination for convenience clause, or an IP assignment clause."
The Magic: It takes a task that would have taken me days of mind-numbing, error-prone reading and condenses it into an afternoon. It outputs a clean spreadsheet with the contract name, the clause, and its analysis. It's incredibly powerful for identifying risk and variance at scale.
The Limitation: The key is that it's highly specialized. It's brilliant at pattern recognition for things it's been trained on. But it lacks true business context. It can tell you a clause is non-standard, but it can't tell you why the founder agreed to it or if that specific customer relationship justifies the risk. It finds the "what," but I still have to do the work to understand the "why" and the "so what?"
2. Generative AI for Q&A (The "Chat with your VDR" Tools)
This is the newer, more experimental, and frankly more exciting category. These are tools that use Large Language Models (LLMs) to allow you to have a conversation with your entire data room. You can connect it to the VDR and just ask questions in plain English.
My Experience: I've been using these in parallel with my manual process. The promise is the holy grail: to be able to ask, "Summarize the key risks outlined in the board minutes from the last six months," or "What were the company's stated revenue goals for 2024, and which documents mention them?"
The Magic (When it Works): When it works, it feels like living in the future. It can synthesize information across different document types—pulling a sentence from a Word doc, a number from a spreadsheet, and a bullet point from a PowerPoint—into a single, coherent answer. It can be an incredible starting point for an investigation.
The Frustration (The Current Reality): The biggest problem right now is trust and reliability. These models can still "hallucinate" or misinterpret context with a scary amount of confidence. I had an experience recently where I asked it to summarize a company's revenue recognition policy. It confidently gave me a beautiful, two-paragraph summary that blended a clause from a customer contract with a footnote from the financial statements. The summary sounded perfectly plausible, but it was subtly wrong in a way that could have had major implications. It created a policy that didn't actually exist.
That's the danger. It's not when the AI says "I don't know." It's when it gives you a confidently wrong answer.
So, my takeaway is this: These tools are becoming essential for accelerating the first draft of an analysis. The AI contract tools are fantastic for rapidly surfacing risks. The generative AI tools are amazing for finding connections you might have missed. But they are not a replacement for deep, manual verification and critical human judgment. You have to treat the output as a very smart, very fast, but sometimes unreliable intern. You still have to check all their work.
Sources
Please describe your ideal set up for Chat with your VDR? Should the chat be on Sequoia side meaning that you're able to connect to a VDR and chat locally with Sequoia references in the background? Or would you prefer being able to chat using the VDR's own chat interface?
S
Sequoia Analyst Mentoring a Founder
Custom Gem
Show thinking
(Alex leans forward, a genuine spark of excitement in his eyes. This question clearly moves from the frustrations of the present to the possibilities of the future.)
That is a fantastic and very sophisticated question. It gets right to the heart of the trust, security, and utility debate surrounding these tools. You've perfectly laid out the two architectural paths.
Hands down, my ideal setup is Option A: a chat interface on the Sequoia side that connects securely to any VDR. And my reasoning for this is about much more than just convenience; it's about building true institutional knowledge.
Let me walk you through why.
1. The Power of Proprietary Context (The "Sequoia Brain")
A VDR-native chat tool can only ever know what's in that specific data room. It's isolated. The real magic, the 10x improvement in my workflow, comes from being able to chat not just with the VDR, but with the VDR in the context of Sequoia's collective knowledge.
Imagine this ideal workflow:
I connect our Sequoia chat tool to the "Project Atlas" VDR via a secure API. Now, I can ask questions that a VDR-native tool could never dream of answering:
"Summarize the key risks in the 'Project Atlas' board minutes. Then, cross-reference them with the risks we identified in our diligence memos for 'Project Galileo' and 'Project Nova' (two other biotech deals we did)."
"Project Atlas is projecting a 24-month sales cycle. How does this compare to the average sales cycle for the other Series A enterprise SaaS companies in our portfolio?"
"This founder's background is in computational biology. Pull up our internal thesis on the future of AI in drug discovery and highlight any claims in the pitch deck that contradict our findings."
See the difference? The VDR data becomes just one input into a much more powerful analytical engine. The chat isn't just with the data room; it's with the entire history and intelligence of our firm, applied to that data room.
2. Control, Customization, and Trust
By having the tool on our side, we control the models, the security protocols, and the user interface. We can fine-tune it for our specific diligence methodology. We're not beholden to a "one-size-fits-all" product developed by a VDR provider.
This also creates a cleaner trust model. From a founder's perspective, it's a very clear transaction:
You grant Sequoia's secure, audited tool read-only API access to your VDR for a limited time.
We pull the necessary data into our environment for analysis.
When diligence is over, the connection is severed.
With a VDR-native tool, the questions get murky. Is the VDR company training its AI models on my queries? Are they analyzing the founder's data for their own purposes? It introduces a third party into the analysis loop that complicates trust for both us and the founder.
3. Cross-Deal Analysis
A Sequoia-side tool would allow us to spot trends across the market that are invisible when looking at one deal at a time. We could ask questions like, "Across the last 20 pitch decks we've analyzed, what is the most common 'go-to-market' strategy for AI-native companies?" This helps us become smarter investors over time. A VDR-native tool, by design, is a silo that prevents this kind of learning.
The main challenge, of course, is on our end—it requires a significant engineering effort to build and maintain these API connections to the dozen or so VDR providers that founders use. But the strategic advantage is so immense that it's worth the effort.
So, my ideal setup is a single interface where the VDR is one data source, our internal research platform is another, and our historical deal data is a third. It becomes more than a "Chat with your VDR" tool; it becomes a "Chat with Sequoia's Brain" tool, applied to a new company. That's the real game-changer.
If you could wave a magic wand and eliminate one specific task or problem from your plate during diligence, what would it be and why?

