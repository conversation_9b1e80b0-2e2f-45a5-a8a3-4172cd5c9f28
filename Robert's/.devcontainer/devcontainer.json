{"name": "docs-workspace", "image": "mcr.microsoft.com/devcontainers/python:3.12", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "20"}}, "customizations": {"vscode": {"extensions": ["ms-python.python", "yzhang.markdown-all-in-one", "neo4j.neo4j-vscode-extension"]}}, "forwardPorts": [7474, 7687], "remoteUser": "vscode", "postCreateCommand": "pip install google-generativeai openai", "remoteEnv": {"GOOGLE_GEMINI_API_KEY": "${localEnv:GOOGLE_GEMINI_API_KEY}", "OPENAI_API_KEY": "${localEnv:OPENAI_API_KEY}", "ANTHROPIC_API_KEY": "${localEnv:ANTHROPIC_API_KEY}", "NEO4J_URI": "neo4j://host.docker.internal:7687", "NEO4J_USERNAME": "neo4j", "NEO4J_PASSWORD": "password", "OPENAI_API_TYPE": "azure", "AZURE_OPENAI_ENDPOINT": "https://ai-scope-openai.openai.azure.com/", "OPENAI_API_VERSION": "2024-02-01"}}