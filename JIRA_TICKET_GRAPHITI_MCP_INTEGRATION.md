# JIRA Ticket: Connect Devcontainer to Neo4j Graphiti and Install Claude Code MCP

## Ticket Details

**Title:** Configure Devcontainer for Neo4j Graphiti Connection and Claude Code MCP Integration

**Type:** Technical Task

**Priority:** High

**Story Points:** 5

**Labels:** `devcontainer`, `neo4j`, `graphiti`, `mcp`, `claude-code`, `docker-networking`

---

## Summary

Configure a development container to connect to an existing Neo4j Graphiti instance running in Docker, and set up the Graphiti MCP (Model Context Protocol) server for use with Claude Code. This will enable AI-assisted development with persistent graph memory capabilities.

---

## Background

The Neo4j Graphiti instance is already running in a Docker container (`neo4j-graphiti-production`) and is accessible via Bolt protocol. The Graphiti MCP server needs to be installed and configured within the devcontainer to enable Claude Code to interact with the knowledge graph.

### Current Infrastructure:
- **Neo4j Container Name:** `neo4j-graphiti-production`
- **Neo4j Internal Port:** 7687 (Bolt protocol)
- **Neo4j External Port:** 7688 (mapped to host)
- **Neo4j Credentials:** 
  - Username: `neo4j`
  - Password: `graphiti2024`
- **Docker Network:** `graphiti-shared-network`

---

## Acceptance Criteria

1. ✅ Devcontainer can connect to Neo4j Graphiti via `bolt://neo4j-graphiti-production:7687`
2. ✅ Graphiti MCP server is installed and configured in the devcontainer
3. ✅ Claude Code can access Graphiti MCP tools
4. ✅ Connection persists across devcontainer rebuilds
5. ✅ Setup is documented and reproducible

---

## Implementation Steps

### Phase 1: Docker Network Configuration

#### 1.1 Connect to Shared Docker Network

```bash
# Check if the shared network exists
docker network ls | grep graphiti-shared-network

# If not exists, create it
docker network create graphiti-shared-network

# Connect your devcontainer to the network
docker network connect graphiti-shared-network <your-devcontainer-name>

# Verify connection
docker network inspect graphiti-shared-network
```

#### 1.2 Update Devcontainer Configuration (Optional - for persistence)

Add to `.devcontainer/devcontainer.json`:
```json
{
  "runArgs": ["--network=graphiti-shared-network"],
  "postCreateCommand": "bash /workspace/setup_graphiti_mcp.sh"
}
```

OR if using docker-compose, add to `.devcontainer/docker-compose.yml`:
```yaml
services:
  dev:
    networks:
      - graphiti-shared-network

networks:
  graphiti-shared-network:
    external: true
```

### Phase 2: Neo4j Connection Testing

#### 2.1 Test Network Connectivity

From within the devcontainer:
```bash
# Test DNS resolution
ping -c 3 neo4j-graphiti-production

# Test port connectivity
nc -zv neo4j-graphiti-production 7687

# Test with Python
python3 -c "
import socket
sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
sock.settimeout(2)
result = sock.connect_ex(('neo4j-graphiti-production', 7687))
print('✅ Connected' if result == 0 else '❌ Failed')
sock.close()
"
```

#### 2.2 Test Neo4j Connection

Create `test_neo4j.py`:
```python
from neo4j import GraphDatabase

uri = "bolt://neo4j-graphiti-production:7687"
driver = GraphDatabase.driver(uri, auth=("neo4j", "graphiti2024"))

with driver.session() as session:
    result = session.run("RETURN 'Connection successful' as status")
    print(result.single()["status"])

driver.close()
```

### Phase 3: Graphiti MCP Installation

#### 3.1 Install Prerequisites

```bash
# Install Python 3.10+
python3 --version

# Install uv package manager
curl -LsSf https://astral.sh/uv/install.sh | sh
source ~/.bashrc  # or restart terminal

# Clone Graphiti repository
cd /workspace
git clone https://github.com/getzep/graphiti.git
cd graphiti/mcp_server

# Install dependencies
uv sync
```

#### 3.2 Create MCP Wrapper Script

Create `/workspace/run_graphiti_mcp_network.py`:
```python
#!/usr/bin/env python3
"""
Network-aware wrapper for Graphiti MCP server that handles Docker networking
"""
import os
import sys
import subprocess
import socket
import time

def test_neo4j_connection(uri, timeout=2):
    """Test if Neo4j is reachable at given URI"""
    try:
        host, port = uri.replace("bolt://", "").split(":")
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((host, int(port)))
        sock.close()
        return result == 0
    except Exception as e:
        sys.stderr.write(f"Connection test error for {uri}: {e}\n")
        return False

# Try different Neo4j URIs in order of preference
neo4j_uris = [
    # 1. Docker network connection (fastest, most reliable)
    "bolt://neo4j-graphiti-production:7687",
    
    # 2. Host connections (fallback)
    "bolt://host.docker.internal:7688",
    
    # 3. Local connections (if running with --network=host)
    "bolt://localhost:7688",
]

# Find working URI with retries
working_uri = None
max_retries = 3
retry_delay = 2

sys.stderr.write("Testing Neo4j connections...\n")

for attempt in range(max_retries):
    if attempt > 0:
        sys.stderr.write(f"Retry attempt {attempt + 1}/{max_retries}...\n")
        time.sleep(retry_delay)
    
    for uri in neo4j_uris:
        sys.stderr.write(f"Testing {uri}... ")
        if test_neo4j_connection(uri):
            working_uri = uri
            sys.stderr.write(f"✓ Connected!\n")
            break
        else:
            sys.stderr.write(f"✗ Failed\n")
    
    if working_uri:
        break

if not working_uri:
    sys.stderr.write("\nError: Could not connect to Neo4j on any URI\n")
    sys.exit(1)

sys.stderr.write(f"\nUsing Neo4j at: {working_uri}\n")

# Set environment variables
env = os.environ.copy()
env.update({
    "NEO4J_URI": working_uri,
    "NEO4J_USER": "neo4j",
    "NEO4J_PASSWORD": "graphiti2024",
    # Add your OpenAI/Azure OpenAI credentials here
    "OPENAI_API_KEY": "your-api-key-here",
    # Or for Azure OpenAI:
    # "AZURE_OPENAI_ENDPOINT": "https://your-endpoint.openai.azure.com/",
    # "AZURE_OPENAI_API_KEY": "your-api-key",
    # "AZURE_OPENAI_DEPLOYMENT_NAME": "gpt-4",
})

# Change to MCP server directory
os.chdir("/workspace/graphiti/mcp_server")

# Get transport type from argument
transport = sys.argv[1] if len(sys.argv) > 1 else "stdio"

# Run the MCP server
cmd = [
    "/home/<USER>/.local/bin/uv",
    "run",
    "graphiti_mcp_server.py",
    "--transport",
    transport
]

# Execute
subprocess.run(cmd, env=env)
```

Make it executable:
```bash
chmod +x /workspace/run_graphiti_mcp_network.py
```

#### 3.3 Install Claude Code (if not already installed)

```bash
# Install Claude Code CLI
npm install -g @anthropic-ai/claude-code

# Or download from https://claude.ai/download
```

### Phase 4: Claude Code MCP Configuration

#### 4.1 Add MCP Server to Claude Code

```bash
# Add at user level (available in all projects)
claude mcp add --scope user graphiti-memory python3 /workspace/run_graphiti_mcp_network.py stdio

# Or add at project level (for team sharing)
claude mcp add --scope project graphiti-memory python3 /workspace/run_graphiti_mcp_network.py stdio
```

#### 4.2 Create Project Configuration (for team sharing)

Create `/workspace/.mcp.json`:
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "python3",
      "args": ["/workspace/run_graphiti_mcp_network.py", "stdio"]
    }
  }
}
```

#### 4.3 Create Setup Script

Create `/workspace/setup_graphiti_mcp.sh`:
```bash
#!/bin/bash
# Setup script for Graphiti MCP in devcontainer

echo "=== Graphiti MCP Setup Script ==="

# Check if MCP is already configured
if claude mcp list | grep -q "graphiti-memory"; then
    echo "✅ Graphiti MCP already installed"
else
    echo "Installing Graphiti MCP server..."
    claude mcp add --scope user graphiti-memory python3 /workspace/run_graphiti_mcp_network.py stdio
    echo "✅ Graphiti MCP installed successfully"
fi

# Test the connection
echo ""
echo "Testing MCP server connection..."
timeout 5 python3 /workspace/run_graphiti_mcp_network.py stdio <<< '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {}, "clientInfo": {"name": "test", "version": "1.0.0"}}}' > /tmp/mcp_test.log 2>&1

if grep -q '"result"' /tmp/mcp_test.log 2>/dev/null; then
    echo "✅ MCP server responding correctly"
else
    echo "⚠️  MCP server may have connection issues"
fi

echo ""
echo "To verify installation, run: claude mcp list"
```

Make executable:
```bash
chmod +x /workspace/setup_graphiti_mcp.sh
```

### Phase 5: Verification

#### 5.1 Test Claude Code Integration

```bash
# Start Claude Code
claude

# In Claude Code, check MCP status
/mcp

# Should see:
# graphiti-memory: ✓ Connected
```

#### 5.2 Available MCP Tools

Once connected, these tools will be available:
- `mcp__graphiti-memory__add_episode` - Add memories to the graph
- `mcp__graphiti-memory__search_nodes` - Search for entities
- `mcp__graphiti-memory__search_facts` - Search for relationships
- `mcp__graphiti-memory__get_episodes` - Retrieve memories
- `mcp__graphiti-memory__delete_episode` - Remove memories
- `mcp__graphiti-memory__delete_entity_edge` - Remove entities/relationships
- `mcp__graphiti-memory__clear_graph` - Clear all data
- `mcp__graphiti-memory__get_status` - Check connection status

#### 5.3 Test MCP Functionality

Ask Claude:
```
Can you check the Graphiti MCP server status?
```

Or directly:
```
Use the mcp__graphiti-memory__get_status tool to verify the connection
```

---

## Troubleshooting Guide

### Issue: Cannot connect to Neo4j

1. **Check network connectivity:**
   ```bash
   docker network ls | grep graphiti-shared-network
   docker inspect <your-container> | jq '.[0].NetworkSettings.Networks'
   ```

2. **Test direct connection:**
   ```bash
   python3 /workspace/run_graphiti_mcp_network.py stdio
   ```

### Issue: MCP tools not available

1. **Restart Claude Code:**
   ```bash
   # Exit Claude (Ctrl+C)
   claude
   ```

2. **Check MCP configuration:**
   ```bash
   claude mcp list
   cat ~/.claude.json | jq '.mcpServers'
   ```

### Issue: Permission errors

1. **Ensure scripts are executable:**
   ```bash
   chmod +x /workspace/run_graphiti_mcp_network.py
   chmod +x /workspace/setup_graphiti_mcp.sh
   ```

---

## Definition of Done

- [ ] Devcontainer connected to `graphiti-shared-network`
- [ ] Neo4j connection verified via `bolt://neo4j-graphiti-production:7687`
- [ ] Graphiti MCP wrapper script created and tested
- [ ] Claude Code MCP configuration completed
- [ ] MCP tools accessible in Claude Code
- [ ] Setup script created for reproducibility
- [ ] Documentation updated with setup instructions
- [ ] Team member can reproduce setup using provided instructions

---

## Notes for Implementation

1. **Security**: Ensure API keys are stored securely (use environment variables or secrets management)
2. **Network Isolation**: The shared Docker network provides isolation from other containers
3. **Performance**: Direct container-to-container communication is faster than routing through host
4. **Persistence**: The `.mcp.json` file can be committed to version control for team sharing

---

## Related Documentation

- [Graphiti Documentation](https://github.com/getzep/graphiti)
- [Claude Code MCP Documentation](https://docs.anthropic.com/en/docs/claude-code/mcp)
- [Neo4j Bolt Protocol](https://neo4j.com/docs/bolt/current/)

---

## Support Contact

For issues with:
- Neo4j connection: Check container logs with `docker logs neo4j-graphiti-production`
- MCP server: Check logs in `~/.cache/claude-cli-nodejs/`
- Claude Code: Run `claude --debug` for detailed logging