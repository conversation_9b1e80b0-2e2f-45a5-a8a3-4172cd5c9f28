{"name": "Business Dev Container", "image": "mcr.microsoft.com/devcontainers/python:3.12", "features": {"ghcr.io/devcontainers/features/node:1": {"version": "20", "nodeGypDependencies": false}}, "customizations": {"vscode": {"extensions": ["ms-python.python"]}, "codespaces": {"openFiles": ["README.md"]}}, "forwardPorts": [5010], "remoteUser": "vscode", "hostRequirements": {"memory": "10gb"}, "postCreateCommand": "pip install -r requirements.txt "}